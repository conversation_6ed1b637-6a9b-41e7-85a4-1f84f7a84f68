@font-face {
  font-family: '思源宋体';
  src: url(https://ztimg.hefei.cc/static/common/fonts/思源宋体.otf);
}
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
  font-size: 3.5vw;
}
.musicbtn {
  width: 10vw;
  height: 10vw;
  top: 3.6vw;
  right: 2.8vw;
  background: url(../img/music.png) no-repeat center center / contain;
  z-index: 11;
}
.warp {
  margin: 0 auto;
  min-height: 170vw;
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: '思源宋体';
  background: linear-gradient(180deg, #fdf9ec 0%, #e3e3e3 100%);
}
.warp .swipe_container {
  width: 100%;
  height: 100%;
}
.warp .page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  color: #352219;
  background: url(../img/bj.jpg) no-repeat center center / 100vw auto;
}
.warp .page .rotation {
  width: 65vw;
  height: 8.8vw;
  position: absolute;
  top: 10vw;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 3;
}
.warp .page .rotation .van-swipe {
  height: 8.8vw;
}
.warp .page .rotation .van-swipe .van-swipe-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.warp .page .rotation p {
  text-align: center;
  white-space: nowrap;
  font-size: 4vw;
}
.warp .page .rotation p span {
  font-size: 4vw;
}
.warp .page .title {
  margin-top: 20vw;
  width: 79.2vw;
  z-index: 2;
}
.warp .page .title2 {
  margin-top: 3vw;
  width: 60vw;
  z-index: 3;
}
.warp .page .start {
  position: absolute;
  bottom: 10vh;
  width: 17.7333vw;
  height: 34.9333vw;
  flex-shrink: 0;
  background: url(../img/start.png) no-repeat center center / 100% 100%;
  z-index: 2;
}
.warp .page .bg2 {
  width: 100vw;
  position: absolute;
  left: 0;
  bottom: 0;
  pointer-events: none;
}
.warp .page .bg3 {
  width: 100vw;
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
}
.warp .page .button_container {
  position: relative;
  z-index: 2;
  margin-top: 50vw;
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0 1vw;
}
.warp .page .button_container .button {
  height: 40vw;
}
.warp .page .tip {
  margin-top: 110vw;
  width: 65.0667vw;
  height: 42.8vw;
  background: url(../img/tip.png) no-repeat center center / 100% 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.warp .page .tip .button5 {
  width: 42.1333vw;
  position: absolute;
  bottom: -5vw;
}
.warp .page .game_area {
  width: 100vw;
  height: 177.8667vw;
  position: relative;
}
.warp .page .game_area .data {
  position: absolute;
  top: 16.6667vw;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8vw;
}
.warp .page .game_area .data .score_area {
  width: 19.7333vw;
  height: 21.0667vw;
  background: url(../img/score_area.png) no-repeat center center / 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 6vw;
  font-weight: bold;
  padding-bottom: 1vw;
}
.warp .page .game_area .data .time_area {
  width: 19.7333vw;
  height: 21.0667vw;
  background: url(../img/time_area.png) no-repeat center center / 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 6vw;
  font-weight: bold;
  padding-bottom: 1vw;
}
.warp .page .game_area .spirit1 {
  width: 9.4667vw;
  position: absolute;
  top: 76vw;
  left: 65vw;
  cursor: pointer;
  transition: transform 0.2s;
}
.warp .page .game_area .spirit1:hover {
  transform: scale(1.1);
}
.warp .page .game_area .spirit2 {
  width: 10.8vw;
  position: absolute;
  top: 72vw;
  left: 84vw;
  cursor: pointer;
  transition: transform 0.2s;
}
.warp .page .game_area .spirit2:hover {
  transform: scale(1.1);
}
.warp .page .game_area .spirit3 {
  width: 11.8667vw;
  position: absolute;
  top: 101vw;
  left: 53vw;
  cursor: pointer;
  transition: transform 0.2s;
}
.warp .page .game_area .spirit3:hover {
  transform: scale(1.1);
}
.warp .page .game_area .spirit4 {
  width: 6.6667vw;
  position: absolute;
  top: 97vw;
  left: 72vw;
  cursor: pointer;
  transition: transform 0.2s;
}
.warp .page .game_area .spirit4:hover {
  transform: scale(1.1);
}
.warp .page .game_area .spirit5 {
  width: 7.8667vw;
  position: absolute;
  top: 100vw;
  left: 88vw;
  cursor: pointer;
  transition: transform 0.2s;
}
.warp .page .game_area .spirit5:hover {
  transform: scale(1.1);
}
.warp .page .game_area .panzi {
  width: 37vw;
  height: 16vw;
  position: absolute;
  top: 129vw;
  left: 51vw;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: 1vw;
  padding: 1vw;
  border-radius: 1vw;
}
.warp .page .game_area .panzi .selected-product {
  width: 8vw;
  height: 8vw;
  cursor: pointer;
  transition: transform 0.2s;
}
.warp .page .game_area .panzi .selected-product img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.warp .page .game_area .panzi .selected-product:hover {
  transform: scale(1.1);
}
.warp .page .game_area .jf {
  width: 16.8vw;
  position: absolute;
  bottom: 50vw;
  right: 24vw;
  cursor: pointer;
  transition: transform 0.2s, opacity 0.2s;
}
.warp .page .game_area .jf:hover {
  transform: scale(1.05);
}
.warp .page .game_area .people {
  height: 76.8vw;
  position: absolute;
  right: 0;
  bottom: -19vw;
  pointer-events: none;
}
.warp .page .game_area .talk {
  width: 67vw;
  min-height: 14.4vw;
  border-radius: 1vw;
  background-color: #f9f1e6;
  padding: 2vw 1vw;
  color: #352219;
  border: 0.2vw solid #d4af37;
  position: absolute;
  top: 148vw;
  left: 2vw;
  font-size: 3.5vw;
  line-height: 1.4;
  box-shadow: 0 0.5vw 1vw rgba(0, 0, 0, 0.1);
}
.warp .page .game_area .talk:after {
  width: 6.1333vw;
  height: 4.6667vw;
  content: '';
  background: url(../img/wb.png) no-repeat center center / 100% 100%;
  position: absolute;
  bottom: -4vw;
  right: 12vw;
}
.warp .page .game_area .talk.result-dialog {
  animation: fadeInOut 2s ease-in-out;
}
.warp .page .game_area .talk.result-dialog.success {
  background-color: #e8f5e8;
  border-color: #4caf50;
}
.warp .page .game_area .talk.result-dialog.fail {
  background-color: #ffeaea;
  border-color: #f44336;
}
.warp .page .game_area .score-change {
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12vw;
  font-weight: bold;
  color: #421a05;
  text-shadow: 2px 2px 0 #ffc74f, -2px -2px 0 #ffc74f, 2px -2px 0 #ffc74f, -2px 2px 0 #ffc74f;
  animation: scoreChangeAnimation 1.5s ease-out forwards;
  z-index: 10;
  pointer-events: none;
}
.warp .page .game_area .score-change.positive {
  color: #421a05;
}
.warp .page .game_area .score-change.negative {
  color: #421a05;
}
.warp .bj2 {
  background-image: url(../img/bj2.jpg);
}
.warp .bj3 {
  background-image: url(../img/bj3.jpg);
}
@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  20% {
    opacity: 1;
    transform: scale(1);
  }
  80% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.9);
  }
}
@keyframes scoreChangeAnimation {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(0) scale(0.5);
  }
  20% {
    opacity: 1;
    transform: translateX(-50%) translateY(-5vw) scale(1.2);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) translateY(-10vw) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-20vw) scale(0.8);
  }
}
.blur {
  filter: blur(1vw);
}
.fc {
  justify-content: center;
}
.area {
  margin-top: -8vw;
  width: 93.6vw;
  height: 126.8vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  background: url(../img/area.png) no-repeat center center / 100% 100%;
}
.area .stit {
  margin-top: 10vw;
  width: 46vw;
}
.area .spirit1 {
  position: absolute;
  width: 30.4vw;
  left: -9vw;
  bottom: -6vw;
}
.area .back {
  position: absolute;
  bottom: -4vw;
  width: 45.3333vw;
}
.area .submit {
  position: absolute;
  bottom: -15vw;
  width: 40vw;
}
.area .rule {
  width: 100%;
  padding: 0 4vw;
  margin: 2vw 0 10vw;
  flex: 1;
  overflow-y: auto;
  line-height: 1.5;
  text-align: justify;
  letter-spacing: -0.1vw;
  position: relative;
}
.area .prize {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .prize .mt5 {
  margin-top: 2vw;
}
.area .prize .info {
  padding: 5vw 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 16vw;
  width: 70vw;
}
.area .prize .info:first-child {
  border-bottom: 1px dashed #215444;
}
.area .prize .info .p2 {
  font-size: 5vw;
  line-height: 7vw;
  max-width: 75vw;
  text-align: center;
}
.area .prize .info .jptit {
  width: 29.467vw;
  margin-bottom: 2vw;
}
.area .prize .edit {
  width: 45.3333vw;
}
.area .form {
  width: 100%;
  padding: 16vw 5vw 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .form .form-item {
  margin-left: 0;
  margin-bottom: 5vw;
  display: flex;
  align-items: center;
}
.area .form .form-item label {
  width: 22vw;
  font-weight: bold;
  font-size: 4.6vw;
  white-space: nowrap;
  color: #352219;
  flex-shrink: 0;
}
.area .form .form-item div input {
  margin-bottom: 3vw;
}
.area .form .form-item div input:nth-last-child(1) {
  margin-bottom: 0;
}
.area .form .form-item .right {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .form .form-item input {
  margin-left: 0.8vw;
  padding-left: 2.5333vw;
  width: 50vw;
  height: 7.7333vw;
  border: 1px #352219 solid;
  flex-shrink: 0;
  opacity: 1;
  color: #352219;
  font-size: 4.6vw;
}
.area .form .form-item input::-webkit-input-placeholder {
  color: #352219;
  opacity: 0.6;
}
.area .form .form-item input:-moz-placeholder {
  color: #352219;
  opacity: 0.6;
}
.area .form .form-item input::-moz-placeholder {
  color: #352219;
  opacity: 0.6;
}
.area .form .form-item input:-ms-input-placeholder {
  color: #352219;
  opacity: 0.6;
}
.area .form .form-item #getArea {
  opacity: 0;
  position: absolute;
  z-index: -1;
}
.area .form .form-footer {
  margin-top: -10vw;
  display: flex;
  width: 200%;
  transform: scale(0.5);
  color: #352219;
}
.area .form .form-footer .fz1 {
  font-size: 6vw;
}
.area .form .form-footer p {
  font-size: 6vw;
  line-height: 1.5;
  text-align: justify;
  letter-spacing: 0.3vw;
}
.area .form .button {
  margin-top: -5vw;
  width: 30.4vw;
}
.area .form .fs {
  align-items: flex-start;
}
.area .form .fs label {
  margin-top: 0.5vw;
}
.area2 {
  margin-top: 0;
  width: 87.2vw;
  height: 136.5333vw;
  background: url(../img/area2.png) no-repeat center center / 100% 100%;
}
.area2 .stit {
  margin-top: -21vw;
  margin-bottom: 10vw;
}
.area2 .back {
  bottom: 8vw;
}
.mask {
  z-index: 10;
  position: fixed;
  top: 0;
  left: 0;
  min-height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(18, 45, 29, 0.3);
  transform: translateX(-50%);
  left: 50%;
  color: #352219;
  font-weight: 300;
}
.mask .popup {
  margin-top: -1vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.mask .popup .back {
  width: 42.6667vw;
  position: absolute;
  bottom: -6vw;
}
.mask .popup1 {
  width: 87.8667vw;
  height: 68.2667vw;
  background: url(../img/popup1.png) no-repeat center top / 100% 100%;
}
.mask .popup2 {
  width: 75.4667vw;
  height: 64.8vw;
  background: url(../img/popup2.png) no-repeat center top / 100% 100%;
}
.mask .popup2 .close2 {
  width: 10.8vw;
  position: absolute;
  top: 0;
  right: -2vw;
}
.mask .popup3 {
  width: 75.4667vw;
  height: 60.8vw;
  background: url(../img/popup3.png) no-repeat center top / 100% 100%;
}
.mask .popup3 .back {
  bottom: 9vw;
}
.mask .popup4 {
  width: 75.4667vw;
  height: 60.8vw;
  background: url(../img/popup4.png) no-repeat center top / 100% 100%;
}
.mask .popup4 .back {
  bottom: 9vw;
}
.mask .popup5 {
  width: 75.4667vw;
  height: 66.1333vw;
  background: url(../img/popup5.png) no-repeat center top / 100% 100%;
  padding-top: 0vw;
}
.mask .popup5 .p3 {
  margin-top: -4vw;
  font-size: 7vw;
  white-space: nowrap;
}
.mask .popup5 .p4 {
  font-size: 7vw;
  white-space: nowrap;
}
.mask .popup5 .back {
  bottom: 9vw;
}
.mask .popup6 {
  width: 75.4667vw;
  height: 66.1333vw;
  background: url(../img/popup6.png) no-repeat center top / 100% 100%;
}
.mask .popup6 .back {
  bottom: 9vw;
}
