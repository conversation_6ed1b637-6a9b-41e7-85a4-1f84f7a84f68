<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>甜润夏日客官来碗茶</title>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/style.min.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/animate.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/<EMAIL>">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
    <script defer src="css/polyfill.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
    <div id="app" class="warp" v-cloak>
        <div class="musicbtn" :class="{ on:on }"  @click="bgClick"></div>
        <!-- 首页 -->
        <van-swipe vertical  v-if="page===1" class="swipe_container" :show-indicators="false" :loop="false">
            <van-swipe-item>
                <div class="page fc" :class="{blur:show}">
                    <div class="rotation animate__animated animate__fadeIn">
                        <van-swipe ref="swipe" class="my-swipe" :autoplay="2000" :show-indicators="false" indicator-color="white" vertical>
                            <van-swipe-item v-for="(item,index) in handleRotation">
                                <p>{{item[0]}}<span>{{item[1]}}</span>{{item[2]}}</p>
                            </van-swipe-item>
                        </van-swipe>
                    </div>
                    <img class="title animate__animated animate__zoomIn" src="img/title.png">
                    <div class="start"></div>
                    <div class="button_container">
                        <img src="img/button2.png" class="button animate__animated animate__fadeInLeft" @click="page=2">
                        <img src="img/button3.png" class="button animate__animated animate__fadeInRight" @click="page=3">
                    </div>
                </div>
            </van-swipe-item>
            <van-swipe-item>
                <div class="page bj2 fc" :class="{blur:show}">
                    <div class="tip">
                        <img src="img/button5.png" class="button5" @click="start">
                    </div>
                </div>
            </van-swipe-item>

        </van-swipe>
        <!-- 活动规则页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===2">
            <img src="img/title.png" class="title2 animate__animated animate__zoomIn">
            <div class="area">
                <img src="img/stit.png" class="stit">
                <div class="rule" v-html="startData.rule"></div>
                <img src="img/button4.png" class="back animate__animated animate__fadeInUp" @click="page=1">
            </div>
        </div>
        <!-- 我的奖品页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===3">
            <img src="img/title.png" class="title2 animate__animated animate__zoomIn">
            <div class="area">
                <img src="img/stit2.png" class="stit">
                <div class="prize">
                    <div class="info">
                        <img src="img/jptit1.png" class="jptit mt5">
                        <p class="p2 p3 mt5" v-if="startData.prize">
                            {{startData.prizeTime}}：{{startData.ad}}</p>
                        <div class="p2" v-if="startData.prize">{{startData.prize}}</div>
                        <p class="p2 p1 mt5" v-else>暂未中奖</p>
                    </div>
                    <div class="info">
                        <img src="img/jptit2.png" class="jptit">
                        <p class="p2 mt5">{{startData.userInfo.name}}&nbsp;&nbsp;{{startData.userInfo.phone}}</p>
                        <p class="p2">{{startData.userInfo.area.split(',').join('')}}</p>
                        <p class="p2">{{startData.userInfo.address}}</p>
                    </div>
                    <img src="img/edit.png" class="edit" @click="edit">
                </div>
                <img src="img/button4.png" class="back animate__animated animate__fadeInUp" @click="page=1">
            </div>
        </div>
        <!-- 登记信息页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===4">
            <div class="area area2">
                <form class="form">
                    <img src="img/stit3.png" class="stit">
                    <div class="form-item">
                        <label>姓　　名:</label>
                        <input type="text" v-model="form.name">
                    </div>
                    <div class="form-item">
                        <label>联系方式:</label>
                        <input type="number" v-model="form.phone">
                    </div>
                    <div class="form-item fs">
                        <label>邮寄地址:</label>
                        <div class="right" @click="focus">
                            <input type="text" placeholder="选择省" v-model="form.area.split(',')[0]" readonly>
                            <input type="text" placeholder="选择市" v-model="form.area.split(',')[1]" readonly>
                            <input type="text" placeholder="选择区" v-model="form.area.split(',')[2]" readonly>
                        </div>
                    </div>
                    <div class="form-item">
                        <label>街道地址:</label>
                        <input type="text" v-model="form.address" @keyup.enter="submit">
                    </div>
                    <div class="form-footer">
                        <p class="fz1">◎</p>
                        <p>免责声明:<br>本活动专题收集的所有信息仅供发放活动奖品使用，在未经得本人同意情况下绝对不会将您的任何资料以任何方式泄露给第三方。由于您自身原因如共享登录账号等导致的个人信息披露，活动方概不负责。
                        </p>
                    </div>
                    <img src="img/button6.png" class="back animate__animated animate__fadeInUp" @click="submit">
                </form>
                <van-popup v-model:show="popupShow" round position="bottom">
                    <van-picker show-toolbar title="请选择地区" :columns="options" default-index="11"
                        v-model="selectedValues" @cancel="popupShow = false" @confirm="onConfirm"></van-picker>
                </van-popup>
            </div>
        </div>
        <!-- 游戏页 -->
        <div class="page bj3 fc" :class="{blur:show}" v-if="page===5">
            <div class="game_area">
                <div class="data">
                    <div class="score_area">{{gameState.score}}</div>
                    <div class="time_area">{{gameState.timeLeft}}s</div>
                </div>
                <!-- 当前客户 -->
                <img v-if="gameState.currentCustomer" :src="`img/${gameData.customers[gameState.currentCustomer].id}.png`" class="people">

                <!-- 对话框 -->
                <div class="talk" v-if="!gameState.showResultDialog && gameState.currentDemand && gameState.currentCustomer"
                     v-html="gameState.currentDemand.customers[gameState.currentCustomer]">
                </div>

                <!-- 结果对话框 -->
                <div class="talk result-dialog" v-if="gameState.showResultDialog"
                     :class="{success: gameState.isSuccess, fail: !gameState.isSuccess}"
                     v-html="gameState.resultDialog">
                </div>

                <!-- 分数变化动画 -->
                <div class="score-change" v-if="gameState.showScoreChange"
                     :class="{positive: gameState.scoreChangeValue > 0, negative: gameState.scoreChangeValue < 0}">
                    {{gameState.scoreChangeValue > 0 ? '+' : ''}}{{gameState.scoreChangeValue}}
                </div>

                <!-- 商品选择区 -->
                <!-- 云纹玫瑰囊 -->
                <img src="img/spirit1.png" class="spirit1" @click="selectProduct('云纹玫瑰囊')">
                <!-- 薄荷香缨囊 -->
                <img src="img/spirit2.png" class="spirit2" @click="selectProduct('薄荷香缨囊')">
                <!-- 玫瑰缠花酥 -->
                <img src="img/spirit3.png" class="spirit3" @click="selectProduct('玫瑰缠花酥')">
                <!-- 玉斛冰泉饮 -->
                <img src="img/spirit4.png" class="spirit4" @click="selectProduct('玉斛冰泉饮')">
                <!-- 玫瑰冰酪 -->
                <img src="img/spirit5.png" class="spirit5" @click="selectProduct('玫瑰冰酪')">

                <!-- 盘子 - 显示已选择的商品 -->
                <div class="panzi">
                    <div v-for="(product, index) in gameState.selectedProducts" :key="index"
                         class="selected-product" @click="removeProduct(product)">
                        <img :src="`img/${product.id}.png`" :alt="product.name">
                    </div>
                </div>

                <!-- 交付按钮 -->
                <img src="img/jf.png" class="jf" @click="deliverProducts"
                     :style="{opacity: gameState.selectedProducts.length > 0 ? 1 : 0.5}">
            </div>
        </div>
        <!-- 提交成功弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===1">
                <div class="popup popup1">
                    <img src="img/button4_2.png" class="back" @click="reload">
                </div>
            </div>
        </transition>
        <!-- 无机会弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===2">
                <div class="popup popup2">
                    <img src="img/close.png" class="close2" @click="reload">
                </div>
            </div>
        </transition>
        <!-- 游戏成功弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===3">
                <div class="popup popup3">
                    <img src="img/button8.png" class="back" @click="getPrize">
                </div>
            </div>
        </transition>
        <!-- 游戏失败弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===4">
                <div class="popup popup4">
                    <img src="img/button9.png" class="back" @click="reload">
                </div>
            </div>
        </transition>
        <!-- 中奖弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===5">
                <div class="popup popup5">
                    <div class="p3" v-html="prizeData.ad"></div>
                    <div class="p4">{{prizeData.prize}}</div>
                    <img src="img/button10.png" class="back" @click="goForm">
                </div>
            </div>
        </transition>
        <!-- 未中奖弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===6">
                <div class="popup popup6">
                    <img src="img/button11.png" class="back" @click="reload">
                </div>
            </div>
        </transition>
    </div>
    <!-- <script src="https://ztimg.hefei.cc/static/common/js/libs/vue.js"></script> -->
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/jquery-3.6.0.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/howler.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/preloadjs.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/dayjs.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/html2canvas.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook1.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook2.js"></script>
    <script>
        const { createApp, ref, watch, nextTick } = Vue
    </script>
    <script>
        let rotation = {$rotation|raw};
        window.startData = {
            rotation:rotation,
            jihui: '{$jihui}',
            prize: '{$prize}',
            ad: '{$ad}',
            prizeTime: '{$prizeTime}',
            userInfo: {
                name: '{$name}',
                phone: '{$phone}',
                area: '{$area}',
                address: '{$address}',
            },
            nickname: '{$nickname}',
            avatar: '{$headimgurl}',
            wlq:'{$wlq}',
            endtime: '{$endtime}',
            writeFlag: '{$writeFlag}',
            rule: `{$zt_rule|raw}`,
        }

        const app = createApp({
            setup() {
                const { on, bgClick } = useBgMusic('123.mp3')//调用景音乐
                setMockPage && setMockPage()//添加案例提示语
                const page = ref(1) //控制页面
                const show = ref(0) //控制弹窗
                const { userInfo, endtime, writeFlag } = startData
                const opportunity = ref((+startData.jihui)) //控制机会
                const handleRotation = startData.rotation.map(item => item.split(',')) // 设置顶部获奖数据
                const start = () => {
                    if (endtime === '1') return vantAlert('活动未开始')
                    if (endtime === '2') return vantAlert('活动已结束')
                    if (opportunity.value >= 1) {
                        page.value = 5
                        opportunity.value--
                        startGame() // 初始化游戏状态
                        defaultHttp('gamestart', { status: 1 })
                    } else {
                        show.value = 2
                    }
                }

                const edit = () => {
                    if (writeFlag === 0) {
                        return vantAlert('活动已结束');
                    } else {
                        goForm()
                    }
                }
                const goForm = () => {
                    page.value = 4
                    show.value = 0
                }

                // 登记信息功能
                const { form, popupShow, options, focus, onConfirm, check, selectedValues } = createdForm()
                form.value = userInfo
                const submit = throttle(async () => {
                    if (!check()) return
                    const res = await defaultHttp('action', Object.assign({ act: 'sub' }, form.value), { status: 1 })
                    if (res.status == 1) {
                        show.value = 1
                    } else {
                        vantAlert(res.msg)
                    }
                })
                // 刷新页面功能
                const { reload, savePage } = useReload()
                if (savePage) { page.value = +savePage }
                // 判断是否是初次进入网页,执行预加载
                const { loadStart, progress, progressShow, startFlag } = cheackStartPopup([])
                loadStart()
                if(startFlag){
                    page.value = 2
                }
                // 检查未领取
                if (startData.wlq === '1') {
                    vant.showConfirmDialog({
                        title: '温馨提示',
                        message: '您已中奖，请尽快完善领奖信息！',
                        confirmButtonText: '去领取',
                        cancelButtonText: '不领取'
                    }).then(() => {
                        show.value = 0
                        page.value = 4
                    })
                }

                const gameEnd = throttle(async () => {
                    const res = await defaultHttp('gameEnd', { cg:gameState.value.score>=100?1:0, timer: 60-gameState.value.timeLeft, score:gameState.value.score }, { status: 1 })
                    if (res.status === 1) {
                        show.value = 3
                    } else if(res.status === 2) {
                        show.value = 4
                    } else {
                        vantAlert(res.msg, reload)
                    }
                })

                // 抽奖逻辑
                const prizeData = ref({ prize: startData.prize, ad: startData.ad, prizeType: 0 })
                const getPrize = throttle(async () => {
                    const res = await defaultHttp('getprize', {}, { status: 1, msg: '抽中奖品', data: { prize: '扑克牌一份', ad: '云水雅玩礼', prizeType: 1 } })
                    if (res.status === 1) {
                        show.value = 5
                        prizeData.value = res.data
                    } else if (res.status === 2) {
                        show.value = 6 //未中奖
                    } else {
                        vantAlert(res.msg, reload)
                    }
                })
                // 游戏数据结构
                const gameData = {
                    // 商品数据
                    products: {
                        '玉斛冰泉饮': { id: 'spirit4', name: '玉斛冰泉饮' },
                        '玫瑰冰酪': { id: 'spirit5', name: '玫瑰冰酪' },
                        '玫瑰缠花酥': { id: 'spirit3', name: '玫瑰缠花酥' },
                        '薄荷香缨囊': { id: 'spirit2', name: '薄荷香缨囊' },
                        '云纹玫瑰囊': { id: 'spirit1', name: '云纹玫瑰囊' }
                    },
                    // 客户类型
                    customers: {
                        '文人': { id: 'people4', name: '文人' },
                        '商贾': { id: 'people3', name: '商贾' },
                        '仕女': { id: 'people2', name: '仕女' },
                        '郎中': { id: 'people1', name: '郎中' }
                    },
                    // 需求数据
                    demands: [
                        // 单品需求 - 玉斛冰泉饮
                        {
                            id: 1,
                            product: ['玉斛冰泉饮'],
                            exp: 10,
                            penalty: 5,
                            customers: {
                                '文人': '<p>连日苦读，暑气攻心，劳烦掌柜赐一盏<span style="color: #d4af37;">清润解暑，带些山野之气</span>的冰饮，可好？</p>',
                                '商贾': '<p>赶路赶得嗓子眼冒烟，快来杯实在<span style="color: #d4af37;">降火降躁的冰饮！</p>',
                                '仕女': '<p>今日与小姐妹逛巷，燥热难耐，想寻一款<span style="color: #d4af37;">不甜腻、滋阴清热的冰饮</span>，解解这暑气。</p>',
                                '郎中': '<p>暑日当以清热为先，听闻贵店有<span style="color: #d4af37;">石斛入饮</span>，不知能否来一盏？</p>'
                            },
                            successDialog: '<p>妙哉！此饮如<span style="color: #d4af37;">空山新雨</span>！</p>',
                            failDialog: '<p>妙则妙矣，只是<span style="color: #ff6b6b;">解不了腹中燥火</span>啊。</p>'
                        },
                        // 单品需求 - 玫瑰冰酪
                        {
                            id: 2,
                            product: ['玫瑰冰酪'],
                            exp: 10,
                            penalty: 5,
                            customers: {
                                '文人': '<p>近来诗思枯涩，欲觅一味<span style="color: #d4af37;">冰润甘芳之品，需有花魂入魄</span>。</p>',
                                '商贾': '<p>可有一份<span style="color: #d4af37;">冰甜顶饱的奶食</span>？莫要那起子虚飘飘的！</p>',
                                '仕女': '<p>午后倦梳妆，想用些<span style="color: #d4af37;">冰沁沁、带花香的甜食</span>提神，切记莫要甜得发腻。</p>',
                                '郎中': '<p>夏月心火易亢，可有<span style="color: #d4af37;">以花入馔的冰食</span>？需得润燥不生湿。</p>'
                            },
                            successDialog: '<p><span style="color: #d4af37;">甜香扑鼻，口感绵密</span>！</p>',
                            failDialog: '<p>掌柜的，你是不是<span style="color: #ff6b6b;">看岔眼了</span>？！</p>'
                        },
                        // 单品需求 - 玫瑰缠花酥
                        {
                            id: 3,
                            product: ['玫瑰缠花酥'],
                            exp: 10,
                            penalty: 5,
                            customers: {
                                '文人': '<p>友相邀，备<span style="color: #d4af37;">茶点</span>，求<span style="color: #d4af37;">酥脆不黏牙，内藏暗香</span>者为佳。</p>',
                                '商贾': '<p>包些赶路能带的<span style="color: #d4af37;">"小零食"</span>！咬得响的，带点<span style="color: #d4af37;">花香</span>更妙。</p>',
                                '仕女': '<p>小姐妹们赏画时缺个<span style="color: #d4af37;">雅致点心</span>，须得<span style="color: #d4af37;">模样精巧、入口化酥</span>的。</p>',
                                '郎中': '<p>可有<span style="color: #d4af37;">以花入馔的酥点</span>？馅料万勿油重。</p>'
                            },
                            successDialog: '<p><span style="color: #d4af37;">酥皮落唇如碎雪</span>！</p>',
                            failDialog: '<p>哎？我要的好像<span style="color: #ff6b6b;">不是这个</span>...</p>'
                        },
                        // 单品需求 - 薄荷香缨囊
                        {
                            id: 4,
                            product: ['薄荷香缨囊'],
                            exp: 10,
                            penalty: 5,
                            customers: {
                                '文人': '<p>书斋<span style="color: #d4af37;">苦夏多蚊虫</span>，求一味<span style="color: #d4af37;">清冽</span>的佩香。</p>',
                                '商贾': '<p>走南闯北要个<span style="color: #d4af37;">提神辟秽的香袋</span>！气味需醒脑！</p>',
                                '仕女': '<p>新裁的素罗衣，少个<span style="color: #d4af37;">清雅佩囊</span>相衬，炎炎夏日来点<span style="color: #d4af37;">沁凉的</span>。</p>',
                                '郎中': '<p>三伏湿浊侵体，可有含<span style="color: #d4af37;">山草药的避瘟香囊</span>？</p>'
                            },
                            successDialog: '<p>嗯~此香囊甚好，<span style="color: #d4af37;">吾喜</span>！</p>',
                            failDialog: '<p>错了！这日头太晒，掌柜的<span style="color: #ff6b6b;">眼神都被晃偏了</span>吧！</p>'
                        },
                        // 单品需求 - 云纹玫瑰囊
                        {
                            id: 5,
                            product: ['云纹玫瑰囊'],
                            exp: 10,
                            penalty: 5,
                            customers: {
                                '文人': '<p>但求<span style="color: #d4af37;">玫瑰香囊</span>不媚不妖，<span style="color: #d4af37;">天青配色</span>，香气可怡情养性。</p>',
                                '商贾': '<p>给家里娘子带个礼物，要<span style="color: #d4af37;">玫瑰香味</span>的配饰！</p>',
                                '仕女': '<p>绣<span style="color: #d4af37;">玫瑰的香囊</span>有否？花瓣得层层叠叠，像真花一样～</p>',
                                '郎中': '<p>贵店可有<span style="color: #d4af37;">以香花入囊</span>的物件？</p>'
                            },
                            successDialog: '<p>妙极！<span style="color: #d4af37;">香气扑鼻</span>！</p>',
                            failDialog: '<p>掌柜是不是<span style="color: #ff6b6b;">还没睡醒</span>？</p>'
                        },
                        // 双品需求 - 玉斛冰泉饮+薄荷香缨囊
                        {
                            id: 6,
                            product: ['玉斛冰泉饮', '薄荷香缨囊'],
                            exp: 15,
                            penalty: 5,
                            customers: {
                                '文人': '<p>入山访友，想备份"双清礼"——既要能<span style="color: #d4af37;">清润解乏</span>的冰饮，再要个<span style="color: #d4af37;">薄荷佩囊。两样都得清清爽爽、气味相投。</span></p>'
                            },
                            successDialog: '<p>妙极！两样凑一起，连<span style="color: #d4af37;">衣裳都跟着清爽了</span>！</p>',
                            failDialog: '<p>这配着少了那点<span style="color: #ff6b6b;">清雅之意</span>！</p>'
                        },
                        // 双品需求 - 玫瑰冰酪+云纹玫瑰囊
                        {
                            id: 7,
                            product: ['玫瑰冰酪', '云纹玫瑰囊'],
                            exp: 15,
                            penalty: 8,
                            customers: {
                                '仕女': '<p>赴花会需备两物：一要随身佩的<span style="color: #d4af37;">花味香囊</span>；二要<span style="color: #d4af37;">解馋的冰点</span>，须<span style="color: #d4af37;">与香囊同料</span>方显巧思。</p>'
                            },
                            successDialog: '<p>厉害！<span style="color: #d4af37;">老板手艺真绝</span>！</p>',
                            failDialog: '<p>掌柜的，你是不是<span style="color: #ff6b6b;">看岔眼了</span>？！</p>'
                        },
                        // 三品需求 - 玉斛冰泉饮+玫瑰缠花酥+薄荷香缨囊
                        {
                            id: 8,
                            product: ['玉斛冰泉饮', '玫瑰缠花酥', '薄荷香缨囊'],
                            exp: 20,
                            penalty: 10,
                            customers: {
                                '商贾': '<p>午后热得头晕脑胀！来碗<span style="color: #d4af37;">可口的斛饮</span>，再配些能填肚子的<span style="color: #d4af37;">甜酥点心</span>，顺道捎个能<span style="color: #d4af37;">提神的香囊</span>，赶路、会客都用得上！</p>'
                            },
                            successDialog: '<p>还是你<span style="color: #d4af37;">靠谱</span>！</p>',
                            failDialog: '<p>拿错了物，倒<span style="color: #ff6b6b;">耽误了我赶路</span>！</p>'
                        }
                    ]
                }

                // 游戏状态
                const gameState = ref({
                    score: 0,
                    timeLeft: 100,
                    currentDemand: null,
                    currentCustomer: null,
                    selectedProducts: [],
                    isGameRunning: false,
                    gameTimer: null,
                    showResultDialog: false,
                    resultDialog: '',
                    isSuccess: false,
                    showScoreChange: false,
                    scoreChangeValue: 0,
                    usedCombinations: [] // 记录已经使用过的需求+客户组合
                })

                // 随机选择需求
                const getRandomDemand = () => {
                    // 生成所有可能的需求+客户组合
                    const allCombinations = []
                    gameData.demands.forEach(demand => {
                        Object.keys(demand.customers).forEach(customer => {
                            allCombinations.push({
                                demandId: demand.id,
                                customer: customer,
                                demand: demand
                            })
                        })
                    })

                    console.log('总组合数:', allCombinations.length)
                    console.log('已使用组合:', gameState.value.usedCombinations)
                    console.log('当前组合:', gameState.value.currentDemand?.id, gameState.value.currentCustomer)

                    // 过滤条件：1. 排除已使用的组合 2. 确保与当前不同
                    let availableCombinations = allCombinations.filter(combo => {
                        const combinationKey = `${combo.demandId}-${combo.customer}`
                        const isUsed = gameState.value.usedCombinations.includes(combinationKey)
                        const isSameAsCurrent = gameState.value.currentDemand && gameState.value.currentCustomer &&
                                              combo.demandId === gameState.value.currentDemand.id &&
                                              combo.customer === gameState.value.currentCustomer
                        return !isUsed && !isSameAsCurrent
                    })

                    console.log('第一次过滤后可用组合数:', availableCombinations.length)

                    // 如果没有可用组合，重置已使用列表（但保留当前组合）
                    if (availableCombinations.length === 0) {
                        console.log('触发重置机制')
                        // 重置已使用组合，只保留当前组合
                        if (gameState.value.currentDemand && gameState.value.currentCustomer) {
                            const currentKey = `${gameState.value.currentDemand.id}-${gameState.value.currentCustomer}`
                            gameState.value.usedCombinations = [currentKey]
                        } else {
                            gameState.value.usedCombinations = []
                        }

                        // 重新过滤
                        availableCombinations = allCombinations.filter(combo => {
                            const combinationKey = `${combo.demandId}-${combo.customer}`
                            const isUsed = gameState.value.usedCombinations.includes(combinationKey)
                            const isSameAsCurrent = gameState.value.currentDemand && gameState.value.currentCustomer &&
                                                  combo.demandId === gameState.value.currentDemand.id &&
                                                  combo.customer === gameState.value.currentCustomer
                            return !isUsed && !isSameAsCurrent
                        })
                        console.log('重置后可用组合数:', availableCombinations.length)
                    }

                    // 如果仍然没有可用组合，说明只有一个组合，直接返回不变
                    if (availableCombinations.length === 0) {
                        console.log('没有可用组合，保持当前状态')
                        return {
                            demand: gameState.value.currentDemand,
                            customer: gameState.value.currentCustomer,
                            dialog: gameState.value.currentDemand?.customers[gameState.value.currentCustomer]
                        }
                    }

                    // 随机选择一个可用组合
                    const selectedCombo = availableCombinations[Math.floor(Math.random() * availableCombinations.length)]
                    const combinationKey = `${selectedCombo.demandId}-${selectedCombo.customer}`

                    console.log('选择的组合:', combinationKey)

                    // 记录这个组合已被使用
                    gameState.value.usedCombinations.push(combinationKey)

                    // 更新游戏状态
                    gameState.value.currentDemand = selectedCombo.demand
                    gameState.value.currentCustomer = selectedCombo.customer

                    return {
                        demand: selectedCombo.demand,
                        customer: selectedCombo.customer,
                        dialog: selectedCombo.demand.customers[selectedCombo.customer]
                    }
                }

                // 开始游戏
                const startGame = () => {
                    gameState.value.score = 0
                    gameState.value.timeLeft = 100
                    gameState.value.selectedProducts = []
                    gameState.value.isGameRunning = true
                    gameState.value.usedCombinations = [] // 重置已使用组合

                    // 生成第一个需求
                    nextTick(() => {
                        getRandomDemand()
                    })

                    // 开始倒计时
                    gameState.value.gameTimer = setInterval(() => {
                        gameState.value.timeLeft--
                        if (gameState.value.timeLeft <= 0) {
                            endGame()
                        }
                    }, 1000)
                }

                // 结束游戏
                const endGame = () => {
                    gameState.value.isGameRunning = false
                    if (gameState.value.gameTimer) {
                        clearInterval(gameState.value.gameTimer)
                        gameState.value.gameTimer = null
                    }
                    gameEnd()
                }
                var pop1 = new Howl({ 
                    src:'pop1.mp3', 
                    preload: true, 
                })
                var pop2 = new Howl({ 
                    src:'pop2.mp3', 
                    preload: true, 
                })
                // 选择商品
                const selectProduct = (productName) => {
                    if (!gameState.value.isGameRunning) return
                    const product = gameData.products[productName]
                    //如果盘子里已经包含该商品，就不能再选
                    if(gameState.value.selectedProducts.some(p => p.name === product.name)) return
                    gameState.value.selectedProducts.push(product)
                    pop1.play()
                }

                // 移除商品
                const removeProduct = (product) => {
                    const index = gameState.value.selectedProducts.indexOf(product)
                    if (index > -1) {
                        gameState.value.selectedProducts.splice(index, 1)
                    }
                }

                // 显示分数变化动画
                const showScoreChange = (value) => {
                    gameState.value.scoreChangeValue = value
                    gameState.value.showScoreChange = true

                    // 1.5秒后隐藏分数变化
                    setTimeout(() => {
                        gameState.value.showScoreChange = false
                    }, 1500)
                }

                // 交付商品
                const deliverProducts = () => {
                    if (!gameState.value.currentDemand || !gameState.value.isGameRunning) return
                    pop2.play()
                    const selectedNames = gameState.value.selectedProducts.map(p => p.name).sort()
                    const requiredNames = gameState.value.currentDemand.product.sort()

                    const isCorrect = selectedNames.length === requiredNames.length &&
                                     selectedNames.every((name, index) => name === requiredNames[index])

                    let scoreChange = 0
                    if (isCorrect) {
                        // 成功 - 根据需求类型给不同分数
                        scoreChange = gameState.value.currentDemand.exp
                        gameState.value.score += scoreChange
                        // 显示成功对话
                        showDialog(gameState.value.currentDemand.successDialog, true)
                    } else {
                        // 失败 - 使用需求数据中的penalty字段
                        scoreChange = -gameState.value.currentDemand.penalty
                        gameState.value.score += scoreChange
                        // 显示失败对话
                        showDialog(gameState.value.currentDemand.failDialog, false)
                    }

                    // 显示分数变化动画
                    showScoreChange(scoreChange)

                    // 清空选择
                    gameState.value.selectedProducts = []
                }

                // 显示对话
                const showDialog = (dialog, isSuccess) => {
                    gameState.value.resultDialog = dialog
                    gameState.value.isSuccess = isSuccess
                    gameState.value.showResultDialog = true

                    // 2秒后隐藏对话
                    setTimeout(() => {
                        gameState.value.showResultDialog = false
                    }, 2000)
                }

                // 游戏逻辑
                return {
                    startData, page, show,
                    handleRotation, edit, goForm,
                    on, bgClick,
                    start, submit, reload,
                    form, popupShow, options, focus, onConfirm, check, selectedValues,
                    prizeData, getPrize,
                    // 游戏相关
                    gameData, gameState, startGame, selectProduct, removeProduct, deliverProducts, getRandomDemand, showScoreChange
                }
            },
        });
        app.use(vant);
        app.mount('#app');
    </script>
        <!--分享-->
{include file="share"/}
</body>

</html>



